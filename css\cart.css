/* Cart Page Styles */

.cart-section {
    padding: 40px 0 60px;
    background: #f8fafc;
    min-height: 60vh;
}

.cart-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e5e7eb;
}

.cart-header h1 {
    font-size: 28px;
    font-weight: 700;
    color: #1f2937;
}

.cart-count {
    color: #6b7280;
    font-size: 16px;
}

.cart-content {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 40px;
    align-items: start;
}

/* Cart Items */
.cart-items {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.cart-item {
    display: grid;
    grid-template-columns: 40px 100px 1fr 150px 120px 120px;
    gap: 20px;
    align-items: center;
    padding: 25px;
    border-bottom: 1px solid #f3f4f6;
    transition: all 0.3s ease;
}

.cart-item:last-child {
    border-bottom: none;
}

.cart-item:hover {
    background: #f8fafc;
}

/* Checkbox */
.item-checkbox {
    position: relative;
}

.item-checkbox input[type="checkbox"] {
    opacity: 0;
    position: absolute;
}

.item-checkbox label {
    display: block;
    width: 20px;
    height: 20px;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.item-checkbox input[type="checkbox"]:checked + label {
    background: #667eea;
    border-color: #667eea;
}

.item-checkbox input[type="checkbox"]:checked + label::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* Item Image */
.item-image {
    width: 100px;
    height: 100px;
    border-radius: 8px;
    overflow: hidden;
    background: #f8fafc;
}

.item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Item Details */
.item-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.item-name {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
    line-height: 1.4;
}

.item-variants {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.item-variants span {
    font-size: 12px;
    color: #6b7280;
    background: #f3f4f6;
    padding: 2px 8px;
    border-radius: 12px;
}

.item-actions {
    display: flex;
    gap: 15px;
    margin-top: 5px;
}

.action-link {
    background: none;
    border: none;
    color: #6b7280;
    font-size: 12px;
    cursor: pointer;
    transition: color 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.action-link:hover {
    color: #667eea;
}

.action-link[data-action="remove"]:hover {
    color: #ef4444;
}

/* Item Price */
.item-price {
    text-align: right;
}

.item-price .current-price {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: #ef4444;
    margin-bottom: 4px;
}

.item-price .old-price {
    font-size: 14px;
    color: #9ca3af;
    text-decoration: line-through;
}

/* Item Quantity */
.item-quantity .quantity-controls {
    display: flex;
    align-items: center;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    overflow: hidden;
    width: fit-content;
}

.item-quantity .qty-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: #f8fafc;
    color: #1f2937;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.item-quantity .qty-btn:hover {
    background: #667eea;
    color: white;
}

.item-quantity .qty-input {
    width: 50px;
    height: 32px;
    border: none;
    text-align: center;
    font-weight: 600;
    background: white;
    font-size: 14px;
}

/* Item Total */
.item-total {
    text-align: right;
}

.total-price {
    font-size: 16px;
    font-weight: 700;
    color: #1f2937;
}

/* Cart Summary */
.cart-summary {
    display: flex;
    flex-direction: column;
    gap: 20px;
    position: sticky;
    top: 120px;
}

.summary-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.summary-card h3 {
    font-size: 20px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 20px;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    font-size: 14px;
}

.summary-row span:first-child {
    color: #6b7280;
}

.summary-row span:last-child {
    font-weight: 600;
    color: #1f2937;
}

.summary-row .discount {
    color: #10b981;
}

.summary-row .shipping-fee {
    color: #10b981;
}

.summary-divider {
    height: 1px;
    background: #e5e7eb;
    margin: 15px 0;
}

.summary-row.total {
    font-size: 18px;
    font-weight: 700;
    color: #1f2937;
    padding-top: 10px;
}

.summary-row.total span:last-child {
    color: #ef4444;
}

/* Promo Code */
.promo-code {
    display: flex;
    gap: 10px;
    margin: 20px 0;
}

.promo-code input {
    flex: 1;
    padding: 10px 12px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    font-size: 14px;
}

.promo-code input:focus {
    outline: none;
    border-color: #667eea;
}

.promo-code .btn {
    padding: 10px 16px;
    font-size: 14px;
    white-space: nowrap;
}

/* Checkout Actions */
.checkout-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 20px;
}

.checkout-btn,
.continue-shopping {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 15px 20px;
    font-weight: 600;
    text-decoration: none;
}

.checkout-btn {
    font-size: 16px;
}

.continue-shopping {
    font-size: 14px;
}

/* Shipping Info */
.shipping-info,
.payment-methods {
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.shipping-info h4,
.payment-methods h4 {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.shipping-info h4 i,
.payment-methods h4 i {
    color: #667eea;
}

.shipping-info ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.shipping-info ul li {
    padding: 6px 0;
    color: #6b7280;
    font-size: 14px;
    position: relative;
    padding-left: 20px;
}

.shipping-info ul li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #10b981;
    font-weight: bold;
}

.payment-icons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.payment-icons img {
    height: 30px;
    border-radius: 4px;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.payment-icons img:hover {
    opacity: 1;
}

/* Related Products */
.related-products {
    padding: 60px 0;
    background: white;
}

/* Active cart icon */
.nav-icon.active {
    color: #667eea;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .cart-content {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .cart-summary {
        position: static;
    }
    
    .cart-item {
        grid-template-columns: 40px 80px 1fr 100px 100px 100px;
        gap: 15px;
        padding: 20px;
    }
    
    .item-image {
        width: 80px;
        height: 80px;
    }
}

@media (max-width: 768px) {
    .cart-item {
        grid-template-columns: 1fr;
        gap: 15px;
        text-align: left;
    }
    
    .item-checkbox {
        order: 1;
    }
    
    .item-image {
        order: 2;
        width: 100px;
        height: 100px;
        margin: 0 auto;
    }
    
    .item-details {
        order: 3;
        text-align: center;
    }
    
    .item-price {
        order: 4;
        text-align: center;
    }
    
    .item-quantity {
        order: 5;
        display: flex;
        justify-content: center;
    }
    
    .item-total {
        order: 6;
        text-align: center;
    }
    
    .promo-code {
        flex-direction: column;
    }
    
    .checkout-actions {
        gap: 10px;
    }
}
