// Product Detail Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initProductImages();
    initProductTabs();
    initProductVariants();
    initQuantityControls();
    initProductActions();
});

// Product Images Functionality
function initProductImages() {
    const mainImage = document.getElementById('mainImage');
    const thumbnails = document.querySelectorAll('.thumbnail');
    
    thumbnails.forEach(thumbnail => {
        thumbnail.addEventListener('click', function() {
            // Remove active class from all thumbnails
            thumbnails.forEach(thumb => thumb.classList.remove('active'));
            
            // Add active class to clicked thumbnail
            this.classList.add('active');
            
            // Update main image
            mainImage.src = this.src.replace('100x100', '500x500');
            
            // Add animation effect
            mainImage.style.opacity = '0';
            setTimeout(() => {
                mainImage.style.opacity = '1';
            }, 150);
        });
    });
    
    // Image zoom effect
    const mainImageContainer = document.querySelector('.main-image');
    if (mainImageContainer) {
        mainImageContainer.addEventListener('mousemove', function(e) {
            const rect = this.getBoundingClientRect();
            const x = ((e.clientX - rect.left) / rect.width) * 100;
            const y = ((e.clientY - rect.top) / rect.height) * 100;
            
            mainImage.style.transformOrigin = `${x}% ${y}%`;
        });
        
        mainImageContainer.addEventListener('mouseenter', function() {
            mainImage.style.transform = 'scale(1.5)';
        });
        
        mainImageContainer.addEventListener('mouseleave', function() {
            mainImage.style.transform = 'scale(1)';
        });
    }
}

// Product Tabs Functionality
function initProductTabs() {
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabPanels = document.querySelectorAll('.tab-panel');
    
    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');
            
            // Remove active class from all tabs and panels
            tabBtns.forEach(tab => tab.classList.remove('active'));
            tabPanels.forEach(panel => panel.classList.remove('active'));
            
            // Add active class to clicked tab and corresponding panel
            this.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
            
            // Smooth scroll to tabs section
            document.querySelector('.product-tabs').scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        });
    });
}

// Product Variants Functionality
function initProductVariants() {
    const variantOptions = document.querySelectorAll('.variant-option');
    
    variantOptions.forEach(option => {
        option.addEventListener('click', function() {
            const variantGroup = this.closest('.variant-group');
            const groupOptions = variantGroup.querySelectorAll('.variant-option');
            
            // Remove active class from all options in this group
            groupOptions.forEach(opt => opt.classList.remove('active'));
            
            // Add active class to clicked option
            this.classList.add('active');
            
            // Update price based on variant (example logic)
            updatePriceBasedOnVariant();
            
            // Add visual feedback
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
    });
}

function updatePriceBasedOnVariant() {
    const activeStorage = document.querySelector('.variant-group:first-child .variant-option.active');
    const currentPriceElement = document.querySelector('.current-price');
    const oldPriceElement = document.querySelector('.old-price');
    
    if (activeStorage && currentPriceElement) {
        const storage = activeStorage.textContent;
        let newPrice, oldPrice;
        
        switch(storage) {
            case '128GB':
                newPrice = '26.990.000đ';
                oldPrice = '32.990.000đ';
                break;
            case '256GB':
                newPrice = '29.990.000đ';
                oldPrice = '35.990.000đ';
                break;
            case '512GB':
                newPrice = '35.990.000đ';
                oldPrice = '41.990.000đ';
                break;
            case '1TB':
                newPrice = '41.990.000đ';
                oldPrice = '47.990.000đ';
                break;
            default:
                newPrice = '29.990.000đ';
                oldPrice = '35.990.000đ';
        }
        
        // Animate price change
        currentPriceElement.style.opacity = '0';
        oldPriceElement.style.opacity = '0';
        
        setTimeout(() => {
            currentPriceElement.textContent = newPrice;
            oldPriceElement.textContent = oldPrice;
            currentPriceElement.style.opacity = '1';
            oldPriceElement.style.opacity = '1';
        }, 200);
    }
}

// Quantity Controls
function initQuantityControls() {
    const minusBtn = document.querySelector('.qty-btn.minus');
    const plusBtn = document.querySelector('.qty-btn.plus');
    const qtyInput = document.querySelector('.qty-input');
    
    if (minusBtn && plusBtn && qtyInput) {
        minusBtn.addEventListener('click', function() {
            let currentValue = parseInt(qtyInput.value);
            if (currentValue > 1) {
                qtyInput.value = currentValue - 1;
                updateQuantityDisplay();
            }
        });
        
        plusBtn.addEventListener('click', function() {
            let currentValue = parseInt(qtyInput.value);
            const maxValue = parseInt(qtyInput.getAttribute('max'));
            if (currentValue < maxValue) {
                qtyInput.value = currentValue + 1;
                updateQuantityDisplay();
            }
        });
        
        qtyInput.addEventListener('change', function() {
            let value = parseInt(this.value);
            const min = parseInt(this.getAttribute('min'));
            const max = parseInt(this.getAttribute('max'));
            
            if (value < min) this.value = min;
            if (value > max) this.value = max;
            
            updateQuantityDisplay();
        });
    }
}

function updateQuantityDisplay() {
    const qtyInput = document.querySelector('.qty-input');
    const stockStatus = document.querySelector('.stock-status');
    
    if (qtyInput && stockStatus) {
        const quantity = parseInt(qtyInput.value);
        const remaining = 15 - quantity;
        
        if (remaining > 0) {
            stockStatus.textContent = `Còn ${remaining} sản phẩm`;
            stockStatus.style.color = '#10b981';
        } else {
            stockStatus.textContent = 'Hết hàng';
            stockStatus.style.color = '#ef4444';
        }
    }
}

// Product Actions
function initProductActions() {
    const addToCartBtn = document.querySelector('.add-to-cart-btn');
    const buyNowBtn = document.querySelector('.buy-now-btn');
    const wishlistBtn = document.querySelector('.wishlist-btn');
    const compareBtn = document.querySelector('.compare-btn');
    
    if (addToCartBtn) {
        addToCartBtn.addEventListener('click', function() {
            const quantity = parseInt(document.querySelector('.qty-input').value);
            const selectedVariants = getSelectedVariants();
            
            // Add loading state
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang thêm...';
            this.disabled = true;
            
            // Simulate API call
            setTimeout(() => {
                // Add to cart logic here
                addToCart(selectedVariants, quantity);
                
                // Reset button
                this.innerHTML = '<i class="fas fa-check"></i> Đã thêm!';
                this.style.background = '#10b981';
                
                // Show success notification
                showNotification('Sản phẩm đã được thêm vào giỏ hàng!', 'success');
                
                // Reset button after 2 seconds
                setTimeout(() => {
                    this.innerHTML = '<i class="fas fa-shopping-cart"></i> Thêm vào giỏ hàng';
                    this.disabled = false;
                    this.style.background = '';
                }, 2000);
            }, 1000);
        });
    }
    
    if (buyNowBtn) {
        buyNowBtn.addEventListener('click', function() {
            const quantity = parseInt(document.querySelector('.qty-input').value);
            const selectedVariants = getSelectedVariants();
            
            // Add to cart and redirect to checkout
            addToCart(selectedVariants, quantity);
            window.location.href = 'checkout.html';
        });
    }
    
    if (wishlistBtn) {
        wishlistBtn.addEventListener('click', function() {
            this.classList.toggle('active');
            const icon = this.querySelector('i');
            
            if (this.classList.contains('active')) {
                icon.classList.remove('far');
                icon.classList.add('fas');
                showNotification('Đã thêm vào danh sách yêu thích!', 'success');
            } else {
                icon.classList.remove('fas');
                icon.classList.add('far');
                showNotification('Đã xóa khỏi danh sách yêu thích!', 'info');
            }
        });
    }
    
    if (compareBtn) {
        compareBtn.addEventListener('click', function() {
            this.classList.toggle('active');
            
            if (this.classList.contains('active')) {
                showNotification('Đã thêm vào danh sách so sánh!', 'success');
            } else {
                showNotification('Đã xóa khỏi danh sách so sánh!', 'info');
            }
        });
    }
}

function getSelectedVariants() {
    const variants = {};
    
    // Get selected storage
    const selectedStorage = document.querySelector('.variant-group:first-child .variant-option.active');
    if (selectedStorage) {
        variants.storage = selectedStorage.textContent;
    }
    
    // Get selected color
    const selectedColor = document.querySelector('.color-options .variant-option.active');
    if (selectedColor) {
        variants.color = selectedColor.getAttribute('title');
    }
    
    return variants;
}

function addToCart(variants, quantity) {
    // Get existing cart from localStorage
    let cart = JSON.parse(localStorage.getItem('cart') || '[]');
    
    // Create product object
    const product = {
        id: 'iphone-15-pro-max',
        name: 'iPhone 15 Pro Max',
        price: 29990000,
        variants: variants,
        quantity: quantity,
        image: document.getElementById('mainImage').src
    };
    
    // Check if product already exists in cart
    const existingIndex = cart.findIndex(item => 
        item.id === product.id && 
        JSON.stringify(item.variants) === JSON.stringify(product.variants)
    );
    
    if (existingIndex > -1) {
        cart[existingIndex].quantity += quantity;
    } else {
        cart.push(product);
    }
    
    // Save to localStorage
    localStorage.setItem('cart', JSON.stringify(cart));
    
    // Update cart badge
    updateCartBadge();
}

function updateCartBadge() {
    const cartBadge = document.querySelector('.cart-icon .badge');
    if (cartBadge) {
        const cart = JSON.parse(localStorage.getItem('cart') || '[]');
        const totalItems = cart.reduce((total, item) => total + item.quantity, 0);
        cartBadge.textContent = totalItems;
        
        if (totalItems > 0) {
            cartBadge.style.display = 'block';
        } else {
            cartBadge.style.display = 'none';
        }
    }
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        z-index: 10000;
        display: flex;
        align-items: center;
        gap: 10px;
        max-width: 400px;
        animation: slideInRight 0.3s ease;
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 5000);
    
    // Close button functionality
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    });
}
