/* Product Detail Page Styles */

/* Breadcrumb */
.breadcrumb {
    background: #f8fafc;
    padding: 15px 0;
    border-bottom: 1px solid #e5e7eb;
}

.breadcrumb nav {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
}

.breadcrumb a {
    color: #6b7280;
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb a:hover {
    color: #667eea;
}

.breadcrumb i {
    color: #d1d5db;
    font-size: 12px;
}

.breadcrumb span {
    color: #1f2937;
    font-weight: 500;
}

/* Product Detail */
.product-detail {
    padding: 40px 0;
    background: white;
}

.product-detail-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: start;
}

/* Product Images */
.product-images {
    position: sticky;
    top: 100px;
}

.main-image {
    position: relative;
    margin-bottom: 20px;
    border-radius: 12px;
    overflow: hidden;
    background: #f8fafc;
    aspect-ratio: 1;
}

.main-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.main-image:hover img {
    transform: scale(1.05);
}

.thumbnail-images {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.thumbnail {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    border: 2px solid transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    object-fit: cover;
}

.thumbnail:hover,
.thumbnail.active {
    border-color: #667eea;
    transform: scale(1.05);
}

/* Product Info */
.product-info {
    padding: 20px 0;
}

.product-badges {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.product-badges .badge {
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    color: white;
}

.badge.sale {
    background: #ef4444;
}

.badge.hot {
    background: #f59e0b;
}

.badge.new {
    background: #10b981;
}

.product-title {
    font-size: 32px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 20px;
    line-height: 1.2;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e5e7eb;
}

.product-rating .stars {
    display: flex;
    gap: 2px;
}

.product-rating .stars i {
    color: #fbbf24;
    font-size: 16px;
}

.rating-score {
    font-weight: 600;
    color: #1f2937;
}

.rating-count {
    color: #6b7280;
    font-size: 14px;
}

.rating-link {
    color: #667eea;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
}

.rating-link:hover {
    text-decoration: underline;
}

/* Product Price */
.product-price {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 30px;
}

.current-price {
    font-size: 28px;
    font-weight: 700;
    color: #ef4444;
}

.old-price {
    font-size: 20px;
    color: #9ca3af;
    text-decoration: line-through;
}

.discount {
    background: #ef4444;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
}

/* Product Variants */
.product-variants {
    margin-bottom: 30px;
}

.variant-group {
    margin-bottom: 20px;
}

.variant-group label {
    display: block;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 10px;
}

.variant-options {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.variant-option {
    padding: 10px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    background: white;
    color: #1f2937;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 60px;
    text-align: center;
}

.variant-option:hover,
.variant-option.active {
    border-color: #667eea;
    background: #667eea;
    color: white;
}

.color-option {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    padding: 0;
    position: relative;
    min-width: auto;
}

.color-option::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    right: 2px;
    bottom: 2px;
    border-radius: 50%;
    background: var(--color, #000);
}

.color-option[data-color="#1f2937"]::before { background: #1f2937; }
.color-option[data-color="#f3f4f6"]::before { background: #f3f4f6; }
.color-option[data-color="#fbbf24"]::before { background: #fbbf24; }
.color-option[data-color="#3b82f6"]::before { background: #3b82f6; }

.color-option.active::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: 14px;
    z-index: 1;
}

/* Product Quantity */
.product-quantity {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 30px;
}

.product-quantity label {
    font-weight: 600;
    color: #1f2937;
}

.quantity-controls {
    display: flex;
    align-items: center;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
}

.qty-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: #f8fafc;
    color: #1f2937;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.qty-btn:hover {
    background: #667eea;
    color: white;
}

.qty-input {
    width: 60px;
    height: 40px;
    border: none;
    text-align: center;
    font-weight: 600;
    background: white;
}

.stock-status {
    color: #10b981;
    font-size: 14px;
    font-weight: 500;
}

/* Product Actions */
.product-actions {
    display: flex;
    gap: 15px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.add-to-cart-btn,
.buy-now-btn {
    flex: 1;
    min-width: 180px;
    padding: 15px 20px;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-icon {
    width: 50px;
    height: 50px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    background: white;
    color: #6b7280;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-icon:hover {
    border-color: #667eea;
    color: #667eea;
    transform: scale(1.05);
}

.btn-icon.active {
    border-color: #ef4444;
    color: #ef4444;
}

/* Product Features */
.product-features {
    border-top: 1px solid #e5e7eb;
    padding-top: 25px;
}

.feature {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.feature i {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}

.feature div strong {
    display: block;
    color: #1f2937;
    font-weight: 600;
    margin-bottom: 2px;
}

.feature div span {
    color: #6b7280;
    font-size: 14px;
}

/* Product Tabs */
.product-tabs {
    padding: 60px 0;
    background: #f8fafc;
}

.tabs-nav {
    display: flex;
    border-bottom: 2px solid #e5e7eb;
    margin-bottom: 40px;
    overflow-x: auto;
}

.tab-btn {
    padding: 15px 25px;
    border: none;
    background: none;
    color: #6b7280;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
    white-space: nowrap;
}

.tab-btn:hover,
.tab-btn.active {
    color: #667eea;
    border-bottom-color: #667eea;
}

.tab-panel {
    display: none;
    background: white;
    padding: 40px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.tab-panel.active {
    display: block;
}

.tab-panel h3 {
    font-size: 24px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 20px;
}

.tab-panel ul {
    list-style: none;
    padding-left: 0;
}

.tab-panel ul li {
    padding: 8px 0;
    padding-left: 20px;
    position: relative;
    color: #4b5563;
}

.tab-panel ul li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #10b981;
    font-weight: bold;
}

.specs-table {
    width: 100%;
    border-collapse: collapse;
}

.specs-table tr {
    border-bottom: 1px solid #e5e7eb;
}

.specs-table td {
    padding: 15px 0;
    vertical-align: top;
}

.specs-table td:first-child {
    font-weight: 600;
    color: #1f2937;
    width: 30%;
}

.specs-table td:last-child {
    color: #4b5563;
}

/* Reviews */
.reviews-summary {
    background: #f8fafc;
    padding: 30px;
    border-radius: 12px;
    text-align: center;
}

.rating-overview {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.rating-score-large {
    font-size: 48px;
    font-weight: 700;
    color: #1f2937;
}

.rating-stars-large {
    display: flex;
    gap: 5px;
}

.rating-stars-large i {
    font-size: 24px;
    color: #fbbf24;
}

.policy-content h4 {
    color: #1f2937;
    font-weight: 600;
    margin: 20px 0 10px 0;
}

.policy-content ul {
    margin-bottom: 20px;
}
