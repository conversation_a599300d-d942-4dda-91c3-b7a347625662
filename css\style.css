/* Reset và Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Poppins", sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f8fafc;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-outline {
  background: transparent;
  color: #667eea;
  border: 2px solid #667eea;
}

.btn-outline:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
}

/* Header Styles */
.header {
  background: white;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.top-bar {
  background: #1f2937;
  color: white;
  padding: 8px 0;
  font-size: 12px;
}

.top-bar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.contact-info span {
  margin-right: 20px;
}

.contact-info i {
  margin-right: 5px;
  color: #667eea;
}

.top-links a {
  color: white;
  text-decoration: none;
  margin-left: 15px;
  transition: color 0.3s ease;
}

.top-links a:hover {
  color: #667eea;
}

/* Navbar */
.navbar {
  padding: 15px 0;
  border-bottom: 1px solid #e5e7eb;
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo a {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #1f2937;
  font-size: 24px;
  font-weight: 700;
}

.logo i {
  font-size: 28px;
  margin-right: 10px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Search Bar */
.search-bar {
  flex: 1;
  max-width: 500px;
  margin: 0 40px;
  position: relative;
}

.search-bar input {
  width: 100%;
  padding: 12px 50px 12px 20px;
  border: 2px solid #e5e7eb;
  border-radius: 25px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.search-bar input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-bar button {
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translateY(-50%);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.search-bar button:hover {
  transform: translateY(-50%) scale(1.1);
}

/* Nav Actions */
.nav-actions {
  display: flex;
  align-items: center;
  gap: 20px;
}

.nav-icon {
  position: relative;
  color: #1f2937;
  font-size: 20px;
  text-decoration: none;
  transition: all 0.3s ease;
}

.nav-icon:hover {
  color: #667eea;
  transform: scale(1.1);
}

.badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ef4444;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
}

/* User Menu */
.user-menu {
  position: relative;
  cursor: pointer;
}

.user-menu i {
  font-size: 20px;
  color: #1f2937;
  transition: color 0.3s ease;
}

.user-menu:hover i {
  color: #667eea;
}

.dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  min-width: 180px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  z-index: 1000;
}

.user-menu:hover .dropdown {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown a {
  display: block;
  padding: 12px 20px;
  color: #1f2937;
  text-decoration: none;
  transition: all 0.3s ease;
  border-bottom: 1px solid #f3f4f6;
}

.dropdown a:last-child {
  border-bottom: none;
}

.dropdown a:hover {
  background: #f8fafc;
  color: #667eea;
  padding-left: 25px;
}

/* Main Menu */
.main-menu {
  background: #1f2937;
  padding: 0;
}

.menu-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.menu-list li {
  position: relative;
}

.menu-list a {
  display: block;
  padding: 15px 25px;
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
  font-weight: 500;
}

.menu-list a:hover,
.menu-list a.active {
  background: #667eea;
  color: white;
}

.menu-list .dropdown:hover .mega-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

/* Mega Menu */
.mega-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  min-width: 600px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 30px;
  display: flex;
  gap: 40px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  z-index: 1000;
}

.menu-column h4 {
  color: #1f2937;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 600;
}

.menu-column a {
  display: block;
  padding: 8px 0;
  color: #6b7280;
  text-decoration: none;
  transition: all 0.3s ease;
  font-size: 14px;
}

.menu-column a:hover {
  color: #667eea;
  padding-left: 10px;
}

/* Hero Section */
.hero {
  position: relative;
  height: 500px;
  overflow: hidden;
  margin-bottom: 60px;
}

.hero-slider {
  height: 100%;
  position: relative;
}

.slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.5s ease;
  display: flex;
  align-items: center;
}

.slide.active {
  opacity: 1;
}

.slide .container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
}

.slide-content {
  flex: 1;
  color: white;
  max-width: 500px;
}

.slide-content h1 {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 20px;
  animation: slideInLeft 1s ease;
}

.slide-content p {
  font-size: 18px;
  margin-bottom: 30px;
  opacity: 0.9;
  animation: slideInLeft 1s ease 0.2s both;
}

.price {
  margin-bottom: 30px;
  animation: slideInLeft 1s ease 0.4s both;
}

.old-price {
  text-decoration: line-through;
  color: rgba(255, 255, 255, 0.7);
  margin-right: 15px;
  font-size: 18px;
}

.new-price {
  font-size: 28px;
  font-weight: 700;
  color: #fbbf24;
}

.slide-image {
  flex: 1;
  text-align: center;
  animation: slideInRight 1s ease 0.6s both;
}

.slide-image img {
  max-width: 100%;
  height: auto;
  filter: drop-shadow(0 20px 40px rgba(0, 0, 0, 0.3));
}

/* Hero Controls */
.hero-controls {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
  pointer-events: none;
}

.hero-controls button {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  color: white;
  font-size: 18px;
  cursor: pointer;
  transition: all 0.3s ease;
  pointer-events: all;
  backdrop-filter: blur(10px);
}

.hero-controls button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.hero-dots {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
}

.dot.active {
  background: white;
  transform: scale(1.2);
}

/* Animations */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Features Section */
.features {
  padding: 60px 0;
  background: white;
  margin-bottom: 60px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.feature-item {
  display: flex;
  align-items: center;
  padding: 30px;
  background: #f8fafc;
  border-radius: 12px;
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
}

.feature-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  background: white;
}

.feature-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  flex-shrink: 0;
}

.feature-icon i {
  font-size: 24px;
  color: white;
}

.feature-content h3 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #1f2937;
}

.feature-content p {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
}

/* Section Header */
.section-header {
  text-align: center;
  margin-bottom: 50px;
}

.section-header h2 {
  font-size: 36px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 15px;
  position: relative;
}

.section-header h2::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
}

.section-header p {
  font-size: 16px;
  color: #6b7280;
  max-width: 600px;
  margin: 0 auto;
}

/* Categories Section */
.categories {
  padding: 60px 0;
  background: #f8fafc;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
}

.category-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
}

.category-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.category-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.category-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.category-card:hover .category-image img {
  transform: scale(1.1);
}

.category-content {
  padding: 25px;
  text-align: center;
}

.category-content h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.category-content p {
  color: #6b7280;
  margin-bottom: 20px;
  font-size: 14px;
}

/* Products Grid */
.products {
  padding: 60px 0;
  background: white;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
}

.product-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  border: 1px solid #f3f4f6;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.product-image {
  position: relative;
  height: 250px;
  overflow: hidden;
  background: #f8fafc;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.product-badge {
  position: absolute;
  top: 15px;
  left: 15px;
  background: #ef4444;
  color: white;
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.product-badge.new {
  background: #10b981;
}

.product-badge.sale {
  background: #f59e0b;
}

.product-actions {
  position: absolute;
  top: 15px;
  right: 15px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  opacity: 0;
  transform: translateX(20px);
  transition: all 0.3s ease;
}

.product-card:hover .product-actions {
  opacity: 1;
  transform: translateX(0);
}

.action-btn {
  width: 40px;
  height: 40px;
  background: white;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.action-btn:hover {
  background: #667eea;
  color: white;
  transform: scale(1.1);
}

.product-content {
  padding: 20px;
}

.product-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
  line-height: 1.4;
}

.product-title a {
  color: inherit;
  text-decoration: none;
  transition: color 0.3s ease;
}

.product-title a:hover {
  color: #667eea;
}

.product-rating {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.stars {
  display: flex;
  margin-right: 8px;
}

.star {
  color: #fbbf24;
  font-size: 14px;
}

.star.empty {
  color: #d1d5db;
}

.rating-text {
  font-size: 12px;
  color: #6b7280;
}

.product-price {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
}

.price-current {
  font-size: 18px;
  font-weight: 700;
  color: #ef4444;
}

.price-old {
  font-size: 14px;
  color: #9ca3af;
  text-decoration: line-through;
}

.add-to-cart {
  width: 100%;
  padding: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.add-to-cart:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* Newsletter Section */
.newsletter {
  padding: 60px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.newsletter-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 40px;
}

.newsletter-text h3 {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 10px;
}

.newsletter-text p {
  font-size: 16px;
  opacity: 0.9;
}

.newsletter-form {
  display: flex;
  gap: 15px;
  min-width: 400px;
}

.newsletter-form input {
  flex: 1;
  padding: 15px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.newsletter-form input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.newsletter-form input:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

.newsletter-form .btn {
  white-space: nowrap;
  background: white;
  color: #667eea;
  font-weight: 600;
}

.newsletter-form .btn:hover {
  background: #f8fafc;
  transform: translateY(-2px);
}

/* Footer */
.footer {
  background: #1f2937;
  color: white;
}

.footer-main {
  padding: 60px 0 40px;
}

.footer-grid {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1.5fr;
  gap: 40px;
}

.footer-logo a {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: white;
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 20px;
}

.footer-logo i {
  font-size: 28px;
  margin-right: 10px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.footer-column p {
  color: #9ca3af;
  line-height: 1.6;
  margin-bottom: 25px;
}

.footer-column h4 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
  color: white;
}

.footer-column ul {
  list-style: none;
}

.footer-column ul li {
  margin-bottom: 12px;
}

.footer-column ul li a {
  color: #9ca3af;
  text-decoration: none;
  transition: all 0.3s ease;
  font-size: 14px;
}

.footer-column ul li a:hover {
  color: #667eea;
  padding-left: 5px;
}

.social-links {
  display: flex;
  gap: 15px;
  margin-top: 25px;
}

.social-links a {
  width: 40px;
  height: 40px;
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #667eea;
  text-decoration: none;
  transition: all 0.3s ease;
}

.social-links a:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #9ca3af;
  font-size: 14px;
}

.contact-item i {
  width: 20px;
  color: #667eea;
  font-size: 16px;
}

.footer-bottom {
  border-top: 1px solid #374151;
  padding: 25px 0;
}

.footer-bottom-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.footer-bottom p {
  color: #9ca3af;
  font-size: 14px;
}

.payment-methods {
  display: flex;
  gap: 10px;
}

.payment-methods img {
  height: 25px;
  border-radius: 4px;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.payment-methods img:hover {
  opacity: 1;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .container {
    padding: 0 15px;
  }

  .nav-content {
    flex-wrap: wrap;
    gap: 20px;
  }

  .search-bar {
    order: 3;
    flex-basis: 100%;
    margin: 0;
    max-width: none;
  }

  .hero-slider .slide .container {
    flex-direction: column;
    text-align: center;
    gap: 30px;
  }

  .slide-content {
    max-width: none;
  }

  .slide-content h1 {
    font-size: 36px;
  }

  .newsletter-content {
    flex-direction: column;
    text-align: center;
    gap: 30px;
  }

  .newsletter-form {
    min-width: auto;
    width: 100%;
    max-width: 500px;
  }

  .footer-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }
}
