// Authentication JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initAuthFunctionality();
});

function initAuthFunctionality() {
    // Password toggle functionality
    const togglePasswordBtns = document.querySelectorAll('.toggle-password');
    togglePasswordBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const input = this.parentElement.querySelector('input');
            const icon = this.querySelector('i');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    });
    
    // Form validation
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
        
        // Real-time validation
        const inputs = loginForm.querySelectorAll('input[required]');
        inputs.forEach(input => {
            input.addEventListener('blur', () => validateField(input));
            input.addEventListener('input', () => clearFieldError(input));
        });
    }
    
    const registerForm = document.getElementById('registerForm');
    if (registerForm) {
        registerForm.addEventListener('submit', handleRegister);
        
        // Real-time validation for register form
        const inputs = registerForm.querySelectorAll('input[required]');
        inputs.forEach(input => {
            input.addEventListener('blur', () => validateField(input));
            input.addEventListener('input', () => clearFieldError(input));
        });
        
        // Password confirmation validation
        const passwordInput = registerForm.querySelector('#password');
        const confirmPasswordInput = registerForm.querySelector('#confirmPassword');
        
        if (passwordInput && confirmPasswordInput) {
            confirmPasswordInput.addEventListener('blur', () => {
                validatePasswordConfirmation(passwordInput, confirmPasswordInput);
            });
        }
    }
    
    // Social login buttons
    const socialBtns = document.querySelectorAll('.social-btn');
    socialBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const provider = this.classList.contains('google-btn') ? 'Google' : 'Facebook';
            handleSocialLogin(provider);
        });
    });
}

function handleLogin(e) {
    e.preventDefault();
    
    const form = e.target;
    const email = form.email.value.trim();
    const password = form.password.value;
    const remember = form.remember?.checked || false;
    
    // Validate form
    if (!validateLoginForm(email, password)) {
        return;
    }
    
    // Show loading state
    const submitBtn = form.querySelector('.auth-btn');
    showLoadingState(submitBtn, 'Đang đăng nhập...');
    
    // Simulate API call
    setTimeout(() => {
        // Mock authentication
        if (email === '<EMAIL>' && password === 'admin123') {
            // Success
            const userData = {
                id: 1,
                name: 'Admin User',
                email: email,
                avatar: 'https://via.placeholder.com/100x100/667eea/ffffff?text=A'
            };
            
            // Store user data
            if (remember) {
                localStorage.setItem('user', JSON.stringify(userData));
            } else {
                sessionStorage.setItem('user', JSON.stringify(userData));
            }
            
            showNotification('Đăng nhập thành công!', 'success');
            
            // Redirect after delay
            setTimeout(() => {
                const returnUrl = new URLSearchParams(window.location.search).get('return') || '../index.html';
                window.location.href = returnUrl;
            }, 1500);
            
        } else {
            // Error
            hideLoadingState(submitBtn, '<i class="fas fa-sign-in-alt"></i> Đăng nhập');
            showNotification('Email hoặc mật khẩu không chính xác!', 'error');
            
            // Highlight error fields
            const emailField = form.querySelector('#email').closest('.form-group');
            const passwordField = form.querySelector('#password').closest('.form-group');
            
            emailField.classList.add('error');
            passwordField.classList.add('error');
        }
    }, 2000);
}

function handleRegister(e) {
    e.preventDefault();
    
    const form = e.target;
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);
    
    // Validate form
    if (!validateRegisterForm(data)) {
        return;
    }
    
    // Show loading state
    const submitBtn = form.querySelector('.auth-btn');
    showLoadingState(submitBtn, 'Đang tạo tài khoản...');
    
    // Simulate API call
    setTimeout(() => {
        // Mock registration
        const userData = {
            id: Date.now(),
            name: data.fullName,
            email: data.email,
            phone: data.phone,
            avatar: 'https://via.placeholder.com/100x100/667eea/ffffff?text=' + data.fullName.charAt(0)
        };
        
        // Store user data
        localStorage.setItem('user', JSON.stringify(userData));
        
        showNotification('Đăng ký thành công! Chào mừng bạn đến với TechMart!', 'success');
        
        // Redirect after delay
        setTimeout(() => {
            window.location.href = '../index.html';
        }, 2000);
    }, 2500);
}

function handleSocialLogin(provider) {
    showNotification(`Đang chuyển hướng đến ${provider}...`, 'info');
    
    // Simulate social login
    setTimeout(() => {
        const userData = {
            id: Date.now(),
            name: `${provider} User`,
            email: `user@${provider.toLowerCase()}.com`,
            avatar: 'https://via.placeholder.com/100x100/667eea/ffffff?text=' + provider.charAt(0)
        };
        
        localStorage.setItem('user', JSON.stringify(userData));
        showNotification(`Đăng nhập ${provider} thành công!`, 'success');
        
        setTimeout(() => {
            window.location.href = '../index.html';
        }, 1500);
    }, 1500);
}

function validateLoginForm(email, password) {
    let isValid = true;
    
    // Email validation
    if (!email) {
        showFieldError('email', 'Vui lòng nhập email hoặc số điện thoại');
        isValid = false;
    } else if (!isValidEmail(email) && !isValidPhone(email)) {
        showFieldError('email', 'Email hoặc số điện thoại không hợp lệ');
        isValid = false;
    }
    
    // Password validation
    if (!password) {
        showFieldError('password', 'Vui lòng nhập mật khẩu');
        isValid = false;
    } else if (password.length < 6) {
        showFieldError('password', 'Mật khẩu phải có ít nhất 6 ký tự');
        isValid = false;
    }
    
    return isValid;
}

function validateRegisterForm(data) {
    let isValid = true;
    
    // Full name validation
    if (!data.fullName || data.fullName.length < 2) {
        showFieldError('fullName', 'Vui lòng nhập họ tên (ít nhất 2 ký tự)');
        isValid = false;
    }
    
    // Email validation
    if (!data.email) {
        showFieldError('email', 'Vui lòng nhập email');
        isValid = false;
    } else if (!isValidEmail(data.email)) {
        showFieldError('email', 'Email không hợp lệ');
        isValid = false;
    }
    
    // Phone validation
    if (!data.phone) {
        showFieldError('phone', 'Vui lòng nhập số điện thoại');
        isValid = false;
    } else if (!isValidPhone(data.phone)) {
        showFieldError('phone', 'Số điện thoại không hợp lệ');
        isValid = false;
    }
    
    // Password validation
    if (!data.password) {
        showFieldError('password', 'Vui lòng nhập mật khẩu');
        isValid = false;
    } else if (data.password.length < 8) {
        showFieldError('password', 'Mật khẩu phải có ít nhất 8 ký tự');
        isValid = false;
    } else if (!isStrongPassword(data.password)) {
        showFieldError('password', 'Mật khẩu phải chứa ít nhất 1 chữ hoa, 1 chữ thường và 1 số');
        isValid = false;
    }
    
    // Confirm password validation
    if (data.password !== data.confirmPassword) {
        showFieldError('confirmPassword', 'Mật khẩu xác nhận không khớp');
        isValid = false;
    }
    
    return isValid;
}

function validateField(input) {
    const value = input.value.trim();
    const fieldName = input.name;
    
    clearFieldError(input);
    
    switch (fieldName) {
        case 'email':
            if (!value) {
                showFieldError(fieldName, 'Vui lòng nhập email');
            } else if (!isValidEmail(value)) {
                showFieldError(fieldName, 'Email không hợp lệ');
            } else {
                showFieldSuccess(input);
            }
            break;
            
        case 'phone':
            if (!value) {
                showFieldError(fieldName, 'Vui lòng nhập số điện thoại');
            } else if (!isValidPhone(value)) {
                showFieldError(fieldName, 'Số điện thoại không hợp lệ');
            } else {
                showFieldSuccess(input);
            }
            break;
            
        case 'password':
            if (!value) {
                showFieldError(fieldName, 'Vui lòng nhập mật khẩu');
            } else if (value.length < 8) {
                showFieldError(fieldName, 'Mật khẩu phải có ít nhất 8 ký tự');
            } else if (!isStrongPassword(value)) {
                showFieldError(fieldName, 'Mật khẩu phải chứa ít nhất 1 chữ hoa, 1 chữ thường và 1 số');
            } else {
                showFieldSuccess(input);
            }
            break;
    }
}

function validatePasswordConfirmation(passwordInput, confirmPasswordInput) {
    const password = passwordInput.value;
    const confirmPassword = confirmPasswordInput.value;
    
    clearFieldError(confirmPasswordInput);
    
    if (!confirmPassword) {
        showFieldError('confirmPassword', 'Vui lòng xác nhận mật khẩu');
    } else if (password !== confirmPassword) {
        showFieldError('confirmPassword', 'Mật khẩu xác nhận không khớp');
    } else {
        showFieldSuccess(confirmPasswordInput);
    }
}

function showFieldError(fieldName, message) {
    const input = document.querySelector(`[name="${fieldName}"]`);
    const formGroup = input.closest('.form-group');
    
    formGroup.classList.remove('success');
    formGroup.classList.add('error');
    
    // Remove existing error message
    const existingError = formGroup.querySelector('.error-message');
    if (existingError) {
        existingError.remove();
    }
    
    // Add new error message
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.innerHTML = `<i class="fas fa-exclamation-circle"></i> ${message}`;
    formGroup.appendChild(errorDiv);
}

function showFieldSuccess(input) {
    const formGroup = input.closest('.form-group');
    formGroup.classList.remove('error');
    formGroup.classList.add('success');
    
    // Remove error message
    const errorMessage = formGroup.querySelector('.error-message');
    if (errorMessage) {
        errorMessage.remove();
    }
}

function clearFieldError(input) {
    const formGroup = input.closest('.form-group');
    formGroup.classList.remove('error', 'success');
    
    const errorMessage = formGroup.querySelector('.error-message');
    if (errorMessage) {
        errorMessage.remove();
    }
}

function showLoadingState(button, text) {
    button.classList.add('loading');
    button.innerHTML = `<i class="fas fa-spinner"></i> ${text}`;
    button.disabled = true;
}

function hideLoadingState(button, originalText) {
    button.classList.remove('loading');
    button.innerHTML = originalText;
    button.disabled = false;
}

// Utility functions
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function isValidPhone(phone) {
    const phoneRegex = /^(\+84|84|0)[3|5|7|8|9][0-9]{8}$/;
    return phoneRegex.test(phone.replace(/\s/g, ''));
}

function isStrongPassword(password) {
    const strongRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/;
    return strongRegex.test(password);
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        z-index: 10000;
        display: flex;
        align-items: center;
        gap: 10px;
        max-width: 400px;
        animation: slideInRight 0.3s ease;
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 5000);
    
    // Close button functionality
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    });
}
