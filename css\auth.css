/* Authentication Pages Styles */

.auth-section {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #e5e7eb 100%);
    display: flex;
    align-items: center;
    padding: 40px 0;
}

.auth-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    max-width: 1000px;
    margin: 0 auto;
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.auth-form-wrapper {
    padding: 50px;
}

.auth-header {
    text-align: center;
    margin-bottom: 40px;
}

.auth-header h1 {
    font-size: 32px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 10px;
}

.auth-header p {
    color: #6b7280;
    font-size: 16px;
}

.auth-form {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-weight: 600;
    color: #1f2937;
    font-size: 14px;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-wrapper i {
    position: absolute;
    left: 15px;
    color: #9ca3af;
    font-size: 16px;
    z-index: 1;
}

.input-wrapper input {
    width: 100%;
    padding: 15px 15px 15px 45px;
    border: 2px solid #e5e7eb;
    border-radius: 10px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: #f8fafc;
}

.input-wrapper input:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.toggle-password {
    position: absolute;
    right: 15px;
    background: none;
    border: none;
    color: #9ca3af;
    cursor: pointer;
    padding: 5px;
    transition: color 0.3s ease;
}

.toggle-password:hover {
    color: #667eea;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 10px 0;
}

.checkbox-wrapper {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    color: #6b7280;
}

.checkbox-wrapper input[type="checkbox"] {
    opacity: 0;
    position: absolute;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    margin-right: 10px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-wrapper input[type="checkbox"]:checked + .checkmark {
    background: #667eea;
    border-color: #667eea;
}

.checkbox-wrapper input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.forgot-link {
    color: #667eea;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: color 0.3s ease;
}

.forgot-link:hover {
    color: #4f46e5;
    text-decoration: underline;
}

.auth-btn {
    padding: 15px 20px;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-top: 10px;
}

.divider {
    position: relative;
    text-align: center;
    margin: 20px 0;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e5e7eb;
}

.divider span {
    background: white;
    padding: 0 20px;
    color: #9ca3af;
    font-size: 14px;
}

.social-login {
    display: flex;
    gap: 15px;
}

.social-btn {
    flex: 1;
    padding: 12px 20px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    background: white;
    color: #6b7280;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.google-btn:hover {
    border-color: #ea4335;
    color: #ea4335;
    background: #fef2f2;
}

.facebook-btn:hover {
    border-color: #1877f2;
    color: #1877f2;
    background: #eff6ff;
}

.auth-footer {
    text-align: center;
    margin-top: 20px;
}

.auth-footer p {
    color: #6b7280;
    font-size: 14px;
}

.auth-footer a {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.auth-footer a:hover {
    color: #4f46e5;
    text-decoration: underline;
}

/* Auth Banner */
.auth-banner {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 50px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    min-height: 600px;
}

.banner-content h2 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 15px;
}

.banner-content p {
    font-size: 16px;
    opacity: 0.9;
    margin-bottom: 40px;
    line-height: 1.6;
}

.banner-features {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 40px;
}

.banner-features .feature {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 14px;
}

.banner-features .feature i {
    width: 20px;
    font-size: 16px;
    opacity: 0.8;
}

.banner-image {
    margin-top: 20px;
}

.banner-image img {
    max-width: 100%;
    height: auto;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

/* Loading State */
.auth-btn.loading {
    pointer-events: none;
    opacity: 0.7;
}

.auth-btn.loading i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Error States */
.form-group.error input {
    border-color: #ef4444;
    background: #fef2f2;
}

.form-group.error .error-message {
    color: #ef4444;
    font-size: 12px;
    margin-top: 5px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.form-group.success input {
    border-color: #10b981;
    background: #f0fdf4;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .auth-container {
        grid-template-columns: 1fr;
        max-width: 500px;
    }
    
    .auth-banner {
        order: -1;
        min-height: auto;
        padding: 40px;
    }
    
    .banner-features {
        flex-direction: row;
        justify-content: center;
        flex-wrap: wrap;
    }
}

@media (max-width: 768px) {
    .auth-section {
        padding: 20px 0;
    }
    
    .auth-container {
        margin: 20px;
        border-radius: 15px;
    }
    
    .auth-form-wrapper {
        padding: 30px;
    }
    
    .auth-banner {
        padding: 30px;
    }
    
    .auth-header h1 {
        font-size: 24px;
    }
    
    .banner-content h2 {
        font-size: 22px;
    }
    
    .social-login {
        flex-direction: column;
    }
    
    .banner-features {
        flex-direction: column;
        align-items: center;
    }
}

@media (max-width: 480px) {
    .auth-form-wrapper {
        padding: 20px;
    }
    
    .auth-banner {
        padding: 20px;
    }
    
    .input-wrapper input {
        padding: 12px 12px 12px 40px;
        font-size: 14px;
    }
    
    .auth-btn {
        padding: 12px 16px;
        font-size: 14px;
    }
}
