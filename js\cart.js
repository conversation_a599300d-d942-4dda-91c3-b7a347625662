// Cart Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initCartFunctionality();
    loadCartItems();
    updateCartSummary();
});

function initCartFunctionality() {
    // Quantity controls
    const quantityControls = document.querySelectorAll('.quantity-controls');
    quantityControls.forEach(control => {
        const minusBtn = control.querySelector('.minus');
        const plusBtn = control.querySelector('.plus');
        const input = control.querySelector('.qty-input');
        
        minusBtn.addEventListener('click', () => {
            let value = parseInt(input.value);
            if (value > 1) {
                input.value = value - 1;
                updateItemTotal(control.closest('.cart-item'));
                updateCartSummary();
            }
        });
        
        plusBtn.addEventListener('click', () => {
            let value = parseInt(input.value);
            const max = parseInt(input.getAttribute('max'));
            if (value < max) {
                input.value = value + 1;
                updateItemTotal(control.closest('.cart-item'));
                updateCartSummary();
            }
        });
        
        input.addEventListener('change', () => {
            let value = parseInt(input.value);
            const min = parseInt(input.getAttribute('min'));
            const max = parseInt(input.getAttribute('max'));
            
            if (value < min) input.value = min;
            if (value > max) input.value = max;
            
            updateItemTotal(control.closest('.cart-item'));
            updateCartSummary();
        });
    });
    
    // Checkbox controls
    const checkboxes = document.querySelectorAll('.item-checkbox input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', () => {
            updateCartSummary();
            updateSelectAllState();
        });
    });
    
    // Action buttons
    const actionLinks = document.querySelectorAll('.action-link');
    actionLinks.forEach(link => {
        link.addEventListener('click', function() {
            const action = this.getAttribute('data-action');
            const cartItem = this.closest('.cart-item');
            
            if (action === 'remove') {
                removeCartItem(cartItem);
            } else if (action === 'wishlist') {
                moveToWishlist(cartItem);
            }
        });
    });
    
    // Promo code
    const promoBtn = document.querySelector('.promo-code .btn');
    if (promoBtn) {
        promoBtn.addEventListener('click', applyPromoCode);
    }
    
    // Checkout button
    const checkoutBtn = document.querySelector('.checkout-btn');
    if (checkoutBtn) {
        checkoutBtn.addEventListener('click', proceedToCheckout);
    }
}

function updateItemTotal(cartItem) {
    const priceElement = cartItem.querySelector('.current-price');
    const quantityInput = cartItem.querySelector('.qty-input');
    const totalElement = cartItem.querySelector('.total-price');
    
    if (priceElement && quantityInput && totalElement) {
        const price = parseFloat(priceElement.textContent.replace(/[^\d]/g, ''));
        const quantity = parseInt(quantityInput.value);
        const total = price * quantity;
        
        totalElement.textContent = formatCurrency(total);
    }
}

function updateCartSummary() {
    const checkedItems = document.querySelectorAll('.item-checkbox input[type="checkbox"]:checked');
    let subtotal = 0;
    let itemCount = 0;
    
    checkedItems.forEach(checkbox => {
        const cartItem = checkbox.closest('.cart-item');
        const totalElement = cartItem.querySelector('.total-price');
        const quantityInput = cartItem.querySelector('.qty-input');
        
        if (totalElement && quantityInput) {
            const total = parseFloat(totalElement.textContent.replace(/[^\d]/g, ''));
            const quantity = parseInt(quantityInput.value);
            
            subtotal += total;
            itemCount += quantity;
        }
    });
    
    // Update subtotal
    const subtotalElement = document.getElementById('subtotal');
    if (subtotalElement) {
        subtotalElement.textContent = formatCurrency(subtotal);
    }
    
    // Calculate discount (example: 10% for orders over 50M)
    let discount = 0;
    if (subtotal > 50000000) {
        discount = subtotal * 0.1;
    }
    
    // Update total
    const total = subtotal - discount;
    const totalElement = document.getElementById('total');
    if (totalElement) {
        totalElement.textContent = formatCurrency(total);
    }
    
    // Update item count
    const itemCountElement = document.getElementById('cartItemCount');
    if (itemCountElement) {
        itemCountElement.textContent = itemCount;
    }
    
    // Update cart badge in header
    updateCartBadge(itemCount);
}

function removeCartItem(cartItem) {
    // Show confirmation
    if (confirm('Bạn có chắc chắn muốn xóa sản phẩm này khỏi giỏ hàng?')) {
        // Add animation
        cartItem.style.transform = 'translateX(-100%)';
        cartItem.style.opacity = '0';
        
        setTimeout(() => {
            cartItem.remove();
            updateCartSummary();
            showNotification('Đã xóa sản phẩm khỏi giỏ hàng!', 'success');
            
            // Check if cart is empty
            const remainingItems = document.querySelectorAll('.cart-item');
            if (remainingItems.length === 0) {
                showEmptyCart();
            }
        }, 300);
    }
}

function moveToWishlist(cartItem) {
    const productName = cartItem.querySelector('.item-name').textContent;
    
    // Add to wishlist logic here
    showNotification(`Đã chuyển "${productName}" vào danh sách yêu thích!`, 'success');
    
    // Remove from cart
    removeCartItem(cartItem);
}

function applyPromoCode() {
    const promoInput = document.querySelector('.promo-code input');
    const promoCode = promoInput.value.trim().toUpperCase();
    
    if (!promoCode) {
        showNotification('Vui lòng nhập mã giảm giá!', 'error');
        return;
    }
    
    // Example promo codes
    const promoCodes = {
        'WELCOME10': { discount: 0.1, description: 'Giảm 10%' },
        'SAVE20': { discount: 0.2, description: 'Giảm 20%' },
        'FREESHIP': { discount: 0, description: 'Miễn phí vận chuyển' }
    };
    
    if (promoCodes[promoCode]) {
        const promo = promoCodes[promoCode];
        showNotification(`Áp dụng mã "${promoCode}" thành công! ${promo.description}`, 'success');
        
        // Apply discount logic here
        updateCartSummary();
        
        // Disable input and button
        promoInput.disabled = true;
        promoInput.value = `${promoCode} - ${promo.description}`;
        document.querySelector('.promo-code .btn').disabled = true;
        document.querySelector('.promo-code .btn').textContent = 'Đã áp dụng';
    } else {
        showNotification('Mã giảm giá không hợp lệ!', 'error');
    }
}

function proceedToCheckout() {
    const checkedItems = document.querySelectorAll('.item-checkbox input[type="checkbox"]:checked');
    
    if (checkedItems.length === 0) {
        showNotification('Vui lòng chọn ít nhất một sản phẩm để thanh toán!', 'error');
        return;
    }
    
    // Show loading state
    const checkoutBtn = document.querySelector('.checkout-btn');
    const originalText = checkoutBtn.innerHTML;
    checkoutBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang xử lý...';
    checkoutBtn.disabled = true;
    
    // Simulate processing
    setTimeout(() => {
        // Redirect to checkout page
        window.location.href = 'checkout.html';
    }, 1500);
}

function showEmptyCart() {
    const cartItems = document.querySelector('.cart-items');
    cartItems.innerHTML = `
        <div class="empty-cart">
            <div class="empty-cart-icon">
                <i class="fas fa-shopping-cart"></i>
            </div>
            <h3>Giỏ hàng của bạn đang trống</h3>
            <p>Hãy thêm sản phẩm vào giỏ hàng để tiếp tục mua sắm</p>
            <a href="../index.html" class="btn btn-primary">Tiếp tục mua sắm</a>
        </div>
    `;
    
    // Add empty cart styles
    const style = document.createElement('style');
    style.textContent = `
        .empty-cart {
            text-align: center;
            padding: 60px 20px;
        }
        
        .empty-cart-icon {
            font-size: 64px;
            color: #d1d5db;
            margin-bottom: 20px;
        }
        
        .empty-cart h3 {
            font-size: 24px;
            color: #1f2937;
            margin-bottom: 10px;
        }
        
        .empty-cart p {
            color: #6b7280;
            margin-bottom: 30px;
        }
    `;
    document.head.appendChild(style);
}

function loadCartItems() {
    // Load cart items from localStorage
    const cart = JSON.parse(localStorage.getItem('cart') || '[]');
    
    if (cart.length === 0) {
        showEmptyCart();
        return;
    }
    
    // Update cart display with actual data
    // This is a simplified version - in a real app, you'd generate the HTML dynamically
    updateCartSummary();
}

function updateSelectAllState() {
    const allCheckboxes = document.querySelectorAll('.item-checkbox input[type="checkbox"]');
    const checkedCheckboxes = document.querySelectorAll('.item-checkbox input[type="checkbox"]:checked');
    
    // Add select all functionality if needed
    const selectAllCheckbox = document.getElementById('selectAll');
    if (selectAllCheckbox) {
        selectAllCheckbox.checked = allCheckboxes.length === checkedCheckboxes.length;
        selectAllCheckbox.indeterminate = checkedCheckboxes.length > 0 && checkedCheckboxes.length < allCheckboxes.length;
    }
}

function updateCartBadge(count) {
    const cartBadge = document.querySelector('.cart-icon .badge');
    if (cartBadge) {
        cartBadge.textContent = count;
        
        if (count > 0) {
            cartBadge.style.display = 'block';
        } else {
            cartBadge.style.display = 'none';
        }
    }
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND',
        minimumFractionDigits: 0
    }).format(amount).replace('₫', 'đ');
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        z-index: 10000;
        display: flex;
        align-items: center;
        gap: 10px;
        max-width: 400px;
        animation: slideInRight 0.3s ease;
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 5000);
    
    // Close button functionality
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    });
}
